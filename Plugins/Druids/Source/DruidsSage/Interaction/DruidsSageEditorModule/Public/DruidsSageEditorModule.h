#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleInterface.h"

#include "ISageActiveObjectProvider.h"

class UDruidsSageExtensionBase;
class UBlueprintEditorToolMenuContext;
class FAssetEditorToolkit;
class FPropertyEditorModule;
class IAssetEditorInstance;
class SWindow;
class SDruidsSageChatShell;
class SDruidsSageChatView;
class FBlueprintContextHandler;
class FTabContextHandler;

// Delegate that will be called when the module needs a ChatShell
DECLARE_DELEGATE_RetVal(TSharedPtr<SCompoundWidget>, FOnCreateChatShell);

class FDruidsSageEditorModule : public IModuleInterface, public ISageActiveObjectProvider
{
public:
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	virtual TWeakObjectPtr<> GetActiveObject() const override;
	
	// Event that can be subscribed to for creating a custom ChatShell
	FOnCreateChatShell OnCreateChatShell;

protected:
	void OnPostEngineInit();

	void ToggleChatWindow();

private:
	TWeakPtr<SWindow> ChatWindowWeakPtr; // To track the floating window
	TSharedRef<SWindow> CreateFloatingChatWindow();

	void RegisterMenus();
	void AddDruidsSageMenuEntries() const;
	FNewToolMenuDelegate AddDruidsSageDynamicMenuEntries() const;

	void OnWindowClosed(const TSharedRef<SWindow>& Window);

	void OnEditorClose();
	void OnEnginePreExit() const;

	static void SaveWindowSize(const FVector2D& NewSize);

	void SaveCurrentChatWindowSize() const;
	void SaveCurrentChatWindowState() const;

	// Window positioning and monitor utilities
	FVector2D ValidateWindowPosition(const FVector2D& Position, const FVector2D& Size) const;
	FString GetCurrentMonitorName(const FVector2D& WindowPosition) const;
	bool IsPositionOnValidMonitor(const FVector2D& Position, const FVector2D& Size) const;

	FPropertyEditorModule* PropertyEditorModule = nullptr;
	TSharedPtr<SCompoundWidget> LastCreatedShell;
	TWeakPtr<SDruidsSageChatView> ActiveChatView; // Reference to the active chat view

	TSharedPtr<FBlueprintContextHandler> BlueprintHandler;
	TSharedPtr<FTabContextHandler> TabHandler;

	void UpdateChatViewContext() const;

	// Updates which plugins are active based on current context
	void UpdateActiveExtensions();
};
