#include "DruidsSageEditorModule.h"

#include <ToolMenus.h>
#include <Widgets/Docking/SDockTab.h>

#include "Editor/Experimental/EditorInteractiveToolsFramework/Public/Behaviors/2DViewportBehaviorTargets.h"

#include "FBlueprintContextHandler.h"
#include "Framework/Application/SlateApplication.h"
#include "Misc/ConfigCacheIni.h"
#include "HAL/PlatformApplicationMisc.h"
#include "GenericPlatform/GenericPlatformApplicationMisc.h"

#include "PropertyEditorModule.h"
#include "FTabContextHandler.h"

#include "LogDruids.h"

#include "SDruidsSageChatView.h"
#include "DruidsSageHelper.h"

#include "Utils/DruidsSageClassDiscovery.h"

#include "SageExtensionDetails.h"
#include "SageExtension.h"
#include "ActiveSageExtensions.h"

#include "SDruidsSageChatShell.h"

#define LOCTEXT_NAMESPACE "FDruidsSageEditorModule"

void FDruidsSageEditorModule::StartupModule()
{
	// Register this module as the editor interface
	FSageActiveObjectProvider::Set(this);
	
	BlueprintHandler = MakeShared<FBlueprintContextHandler>();
	BlueprintHandler->SetContextUpdateCallback([this]()
	{
		UpdateChatViewContext();
	});
	
	TabHandler = MakeShared<FTabContextHandler>();
	TabHandler->SetContextUpdateCallback([this]()
	{
		UpdateChatViewContext();
	});
	
	const FSimpleDelegate RegisterDelegate = FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FDruidsSageEditorModule::RegisterMenus);
	UToolMenus::RegisterStartupCallback(RegisterDelegate);

	RegisterMenus();

	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FDruidsSageEditorModule::OnPostEngineInit);

	// Register detail customization
	FPropertyEditorModule& PropertyModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");
	PropertyModule.RegisterCustomClassLayout(
		USageExtension::StaticClass()->GetFName(),
		FOnGetDetailCustomizationInstance::CreateStatic(&FSageExtensionDetails::MakeInstance)
	);

	// Initialize the class discovery cache
	UDruidsSageClassDiscovery::Initialize();
}

void FDruidsSageEditorModule::ShutdownModule()
{
	FSageActiveObjectProvider::Set(nullptr);
	
	if (BlueprintHandler.IsValid())
	{
		BlueprintHandler.Reset();
	}

	if (TabHandler.IsValid())
	{
		TabHandler.Reset();
	}
	
	ActiveChatView = nullptr;
	LastCreatedShell = nullptr;

	if (GEditor)
	{
		GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetOpenedInEditor().RemoveAll(this);
		GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetClosedInEditor().RemoveAll(this);

		GEditor->OnEditorClose().RemoveAll(this);
	}

	// Clean up the window if it exists
	if (ChatWindowWeakPtr.IsValid())
	{
		ChatWindowWeakPtr.Pin()->RequestDestroyWindow();
		ChatWindowWeakPtr.Reset();
	}

	UToolMenus::UnRegisterStartupCallback(this);
	UToolMenus::UnregisterOwner(this);

	FCoreDelegates::OnPreExit.RemoveAll(this);

	if (FModuleManager::Get().IsModuleLoaded("PropertyEditor"))
	{
		FPropertyEditorModule& PropertyModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");
		PropertyModule.UnregisterCustomClassLayout(USageExtension::StaticClass()->GetFName());
	}
}

void FDruidsSageEditorModule::OnPostEngineInit()
{
	if (GEditor)
	{
		GEditor->OnEditorClose().AddRaw(this, &FDruidsSageEditorModule::OnEditorClose);
	}
}

TSharedRef<SWindow> FDruidsSageEditorModule::CreateFloatingChatWindow()
{
	// Load saved options
	FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();

	// Ensure minimum window size
	float WindowWidth = FMath::Max(Options.WindowWidth, Options.MinWindowWidth);
	float WindowHeight = FMath::Max(Options.WindowHeight, Options.MinWindowHeight);
	FVector2D InitialSize(WindowWidth, WindowHeight);

	// Validate and adjust window position
	FVector2D WindowPosition(Options.WindowPositionX, Options.WindowPositionY);
	if (Options.WindowPositionX >= 0.0f && Options.WindowPositionY >= 0.0f)
	{
		// Check if the saved position is still valid (monitor might have been disconnected)
		if (!IsPositionOnValidMonitor(WindowPosition, InitialSize))
		{
			UE_LOG(LogDruidsSage, Warning, TEXT("Saved window position is no longer valid (monitor may have been disconnected). Using default position."));
			WindowPosition = FVector2D(-1.0f, -1.0f); // Use default positioning
		}
		else
		{
			// Validate the position is fully on screen
			WindowPosition = ValidateWindowPosition(WindowPosition, InitialSize);
		}
	}

	// Create the window
	TSharedRef<SWindow> NewWindow = SNew(SWindow)
		.Title(FText::FromString(TEXT("Druids Sage Chat")))
		.ClientSize(InitialSize) // Use validated size
		.IsTopmostWindow(true)           // Always on top
		.SupportsMaximize(true)         // Allow maximization
		.SupportsMinimize(false)          // Do not allow minimization
		.SizingRule(ESizingRule::UserSized) // Allow resizing
		.IsInitiallyMaximized(false)
		.HasCloseButton(true);

	// Set the content
	if (OnCreateChatShell.IsBound())
	{
		LastCreatedShell = OnCreateChatShell.Execute();
	}

	if (LastCreatedShell.IsValid())
	{
		SDruidsSageChatShell *ChatShell = static_cast<SDruidsSageChatShell*>(LastCreatedShell.Get());
		ActiveChatView = ChatShell->GetCurrentView();
		ChatShell->OnMessageSending.BindRaw(this, &FDruidsSageEditorModule::UpdateActiveExtensions);

		NewWindow->SetContent(LastCreatedShell.ToSharedRef());
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Error, TEXT("FDruidsSageEditorModule::CreateFloatingChatWindow() - ChatShell was not created"));
	}

	UpdateChatViewContext();

	NewWindow->SetOnWindowClosed(FOnWindowClosed::CreateRaw(this, &FDruidsSageEditorModule::OnWindowClosed));

	// Add the window to the slate application
	FSlateApplication::Get().AddWindow(NewWindow);

	// Set window position after adding to application (if we have a valid saved position)
	if (WindowPosition.X >= 0.0f && WindowPosition.Y >= 0.0f)
	{
		NewWindow->MoveWindowTo(WindowPosition);
	}

	return NewWindow;
}

void FDruidsSageEditorModule::SaveWindowSize(const FVector2D& NewSize)
{
	// Save the window size to config
	if (GConfig)
	{
		GConfig->SetVector2D(
			TEXT("DruidsSage.ChatWindow"),
			TEXT("Size"),
			NewSize,
			GEditorPerProjectIni
		);
		GConfig->Flush(false, GEditorPerProjectIni);
	}
}

void FDruidsSageEditorModule::SaveCurrentChatWindowSize() const
{
	if (ChatWindowWeakPtr.IsValid())
	{
		TSharedPtr<SWindow> Window = ChatWindowWeakPtr.Pin();
		if (Window.IsValid())
		{
			SaveWindowSize(Window->GetClientSizeInScreen());
		}
	}
}

void FDruidsSageEditorModule::OnWindowClosed(const TSharedRef<SWindow>& Window)
{
	SaveCurrentChatWindowState();

	if (ChatWindowWeakPtr.IsValid() && ChatWindowWeakPtr.Pin() == Window)
	{
		// Clear the weak pointer
		ChatWindowWeakPtr.Reset();
	}
}

void FDruidsSageEditorModule::UpdateChatViewContext() const
{
	if (ActiveChatView.IsValid())
	{
		// Get Tab Context from TabHandler
		if (TabHandler.IsValid())
		{
			FString TabDisplayContext;
			FString TabFullContext;
			TWeakObjectPtr ActiveObject = TabHandler->RefreshTabContext(TabFullContext, TabDisplayContext);
			
			ActiveChatView.Pin()->SetTabContext(TabFullContext, TabDisplayContext);
			ActiveChatView.Pin()->SetActiveObject(ActiveObject);
		}
		
		// Get BP Context from BlueprintHandler
		if (BlueprintHandler.IsValid() && BlueprintHandler->IsBlueprintFocused())
		{
			FString BPFullContext;
			FString BPDisplayContext;
			BlueprintHandler->GetBlueprintContext(BPFullContext, BPDisplayContext);
			ActiveChatView.Pin()->SetBPContext(BPFullContext, BPDisplayContext);
		}
		else
		{
			ActiveChatView.Pin()->SetBPContext(TEXT(""), TEXT(""));
		}
	}
	else
	{
		UE_LOG(LogDruidsSage_Internal, Display, TEXT("UpdateChatViewContext called but ActiveChatView is not valid."));
	}
}

void FDruidsSageEditorModule::OnEditorClose()
{
	if (TabHandler.IsValid())
	{
		TabHandler->Cleanup();
	}
}

void FDruidsSageEditorModule::OnEnginePreExit() const
{
	SaveCurrentChatWindowState();
}

void FDruidsSageEditorModule::ToggleChatWindow()
{
	// Check if the window already exists and is valid
	if (ChatWindowWeakPtr.IsValid() && ChatWindowWeakPtr.Pin()->IsVisible())
	{
		// Window is open, so close it
		ChatWindowWeakPtr.Pin()->RequestDestroyWindow();
		ChatWindowWeakPtr.Reset();
		return;
	}

	// Window doesn't exist or isn't visible, so create/open it
	TSharedRef<SWindow> NewWindow = CreateFloatingChatWindow();
	ChatWindowWeakPtr = NewWindow; // Store the weak pointer
}

void FDruidsSageEditorModule::RegisterMenus()
{
	FToolMenuOwnerScoped OwnerScoped(this);

	const FName AppStyleName = FAppStyle::GetAppStyleSetName();

	// Add a menu entry under the "Tools" menu
	UToolMenu* MenuTool = UToolMenus::Get()->ExtendMenu("LevelEditor.MainMenu.Tools");
	FToolMenuSection& Section = MenuTool->AddSection("DruidsSageSection", LOCTEXT("DruidsSageSection", "DruidsSage"));
	Section.AddMenuEntry(
		"ToggleDruidsSageChat",
		LOCTEXT("DruidsSageChatLabel", "Druids Sage Chat"),
		LOCTEXT("DruidsSageChatTooltip", "Toggle DruidsSage Chat Window"),
		FSlateIcon(AppStyleName, "DerivedData.ResourceUsage"),
		FUIAction(
			FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::ToggleChatWindow)
		)
	);

	// Add a toolbar button
	UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu("LevelEditor.LevelEditorToolBar.PlayToolBar");
	if (ToolbarMenu)
	{
		FToolMenuSection& ToolbarSection = ToolbarMenu->FindOrAddSection("DruidsSage");
		FToolMenuEntry& Entry = ToolbarSection.AddEntry(FToolMenuEntry::InitToolBarButton(
			"ToggleDruidsSageChat",
			FUIAction(
				FExecuteAction::CreateRaw(this, &FDruidsSageEditorModule::ToggleChatWindow)
			),
			LOCTEXT("DruidsSageChatLabel", "Druids Sage"),
			LOCTEXT("DruidsSageChatTooltip", "Toggle DruidsSage Chat Window"),
			FSlateIcon(AppStyleName, "DerivedData.ResourceUsage")
		));
	}
}

TWeakObjectPtr<> FDruidsSageEditorModule::GetActiveObject() const
{
	return TabHandler.IsValid() ? TabHandler->GetActiveObject() : TWeakObjectPtr();
}

void FDruidsSageEditorModule::SaveCurrentChatWindowState() const
{
	if (ChatWindowWeakPtr.IsValid())
	{
		TSharedPtr<SWindow> Window = ChatWindowWeakPtr.Pin();
		if (Window.IsValid())
		{
			// Get current window state
			FVector2D WindowSize = Window->GetClientSizeInScreen();
			FVector2D WindowPosition = Window->GetPositionInScreen();
			FString MonitorName = GetCurrentMonitorName(WindowPosition);

			// Create options structure with current values
			FDruidsSageCommonOptions Options = UDruidsSageHelper::LoadCommonOptionsFromIni();
			Options.WindowWidth = WindowSize.X;
			Options.WindowHeight = WindowSize.Y;
			Options.WindowPositionX = WindowPosition.X;
			Options.WindowPositionY = WindowPosition.Y;
			Options.MonitorName = MonitorName;

			// Save to INI file
			UDruidsSageHelper::SaveWindowOptionsToIni(Options);
		}
	}
}

FVector2D FDruidsSageEditorModule::ValidateWindowPosition(const FVector2D& Position, const FVector2D& Size) const
{
	// Get the work area of all monitors
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	FVector2D ValidatedPosition = Position;

	// Check if the window would be completely off-screen
	bool bFoundValidMonitor = false;
	for (const FMonitorInfo& Monitor : DisplayMetrics.MonitorInfo)
	{
		const FPlatformRect& MonitorRect = Monitor.WorkArea;

		// Check if any part of the window would be visible on this monitor
		// Convert to simple bounds checking
		float WindowLeft = Position.X;
		float WindowTop = Position.Y;
		float WindowRight = Position.X + Size.X;
		float WindowBottom = Position.Y + Size.Y;

		// Check if rectangles intersect
		bool bIntersects = !(WindowRight < MonitorRect.Left ||
		                    WindowLeft > MonitorRect.Right ||
		                    WindowBottom < MonitorRect.Top ||
		                    WindowTop > MonitorRect.Bottom);

		if (bIntersects)
		{
			bFoundValidMonitor = true;

			// Ensure the window is not positioned off the edges of this monitor
			ValidatedPosition.X = FMath::Clamp(Position.X, (float)MonitorRect.Left, (float)MonitorRect.Right - Size.X);
			ValidatedPosition.Y = FMath::Clamp(Position.Y, (float)MonitorRect.Top, (float)MonitorRect.Bottom - Size.Y);
			break;
		}
	}

	// If no valid monitor found, use primary monitor
	if (!bFoundValidMonitor && DisplayMetrics.MonitorInfo.Num() > 0)
	{
		const FPlatformRect& PrimaryMonitor = DisplayMetrics.PrimaryDisplayWorkAreaRect;
		ValidatedPosition.X = (float)PrimaryMonitor.Left + 100; // Small offset from edge
		ValidatedPosition.Y = (float)PrimaryMonitor.Top + 100;
	}

	return ValidatedPosition;
}

FString FDruidsSageEditorModule::GetCurrentMonitorName(const FVector2D& WindowPosition) const
{
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	// Find which monitor contains the window position
	for (const FMonitorInfo& Monitor : DisplayMetrics.MonitorInfo)
	{
		if (Monitor.WorkArea.ContainsPoint(WindowPosition))
		{
			return Monitor.Name;
		}
	}

	// If not found on any monitor, return primary monitor name
	if (DisplayMetrics.MonitorInfo.Num() > 0)
	{
		return DisplayMetrics.PrimaryDisplayWorkAreaRect.Name;
	}

	return TEXT("Unknown");
}

bool FDruidsSageEditorModule::IsPositionOnValidMonitor(const FVector2D& Position, const FVector2D& Size) const
{
	FDisplayMetrics DisplayMetrics;
	FSlateApplication::Get().GetDisplayMetrics(DisplayMetrics);

	// Check if the window would be visible on any monitor
	for (const FMonitorInfo& Monitor : DisplayMetrics.MonitorInfo)
	{
		FSlateRect MonitorRect = Monitor.WorkArea;
		FSlateRect WindowRect(Position.X, Position.Y, Position.X + Size.X, Position.Y + Size.Y);

		// If any part of the window intersects with this monitor, it's valid
		if (FSlateRect::DoRectanglesIntersect(WindowRect, MonitorRect))
		{
			return true;
		}
	}

	return false;
}

void FDruidsSageEditorModule::UpdateActiveExtensions()
{
	TWeakObjectPtr<UObject> ActiveObject = GetActiveObject();
	TArray<TWeakObjectPtr<USageExtension>> ActiveExtensions = FActiveSageExtensions::Get().
		GetActiveExtensionsForContext(ActiveObject);
	TArray<TSharedPtr<FDruidsSageExtensionDefinition>> ActiveExtensionsDefinitions = FActiveSageExtensions::Get().
		GetDefinitionsFromExtensions(ActiveExtensions);
	
	// Update the chat view with new extensions
	if (ActiveChatView.IsValid())
	{
		ActiveChatView.Pin()->SetActiveExtensionDefinitions(ActiveExtensionsDefinitions);
	}
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FDruidsSageEditorModule, DruidsSageEditorModule)
