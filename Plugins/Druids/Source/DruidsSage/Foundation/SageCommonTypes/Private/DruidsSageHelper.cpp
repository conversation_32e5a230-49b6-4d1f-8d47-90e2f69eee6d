#include "DruidsSageHelper.h"

#include "LogDruids.h"

#include "Misc/Paths.h"
#include "Misc/ConfigCacheIni.h"

#ifdef UE_INLINE_GENERATED_CPP_BY_NAME
#include UE_INLINE_GENERATED_CPP_BY_NAME(DruidsSageHelper)
#endif

const FName UDruidsSageHelper::RoleToName(const EDruidsSageChatRole Role)
{
	switch (Role)
	{
	case EDruidsSageChatRole::Assistant:
		return "assistant";

	case EDruidsSageChatRole::User:
		return "user";

	case EDruidsSageChatRole::System:
		return "system";

	default:
		break;
	}

	return NAME_None;
}

const EDruidsSageChatRole UDruidsSageHelper::NameToRole(const FName Role)
{
	if (Role.IsEqual("user", ENameCase::IgnoreCase))
	{
		return EDruidsSageChatRole::User;
	}
	if (Role.IsEqual("assistant", ENameCase::IgnoreCase) || Role.IsEqual("Sage", ENameCase::IgnoreCase))
	{
		return EDruidsSageChatRole::Assistant;
	}
	if (Role.IsEqual("system", ENameCase::IgnoreCase))
	{
		return EDruidsSageChatRole::System;
	}

	return EDruidsSageChatRole::User;
}

const FDruidsSageCommonOptions UDruidsSageHelper::LoadCommonOptionsFromIni()
{
	FDruidsSageCommonOptions Options;
	FString IniFilePath = FPaths::Combine(FPaths::ProjectDir(), TEXT("DruidsSageConfig.ini"));

	// Load the .ini file
	FConfigFile ConfigFile;
	if (FConfigCacheIni::LoadLocalIniFile(ConfigFile, *IniFilePath, false))
	{
		float WindowWidth;
		if (ConfigFile.GetFloat(TEXT("DruidsSage"), TEXT("WindowWidth"), WindowWidth))
		{
			Options.WindowWidth = WindowWidth;
		}

		float WindowHeight;
		if (ConfigFile.GetFloat(TEXT("DruidsSage"), TEXT("WindowHeight"), WindowHeight))
		{
			Options.WindowHeight = WindowHeight;
		}

		float WindowPositionX;
		if (ConfigFile.GetFloat(TEXT("DruidsSage"), TEXT("WindowPositionX"), WindowPositionX))
		{
			Options.WindowPositionX = WindowPositionX;
		}

		float WindowPositionY;
		if (ConfigFile.GetFloat(TEXT("DruidsSage"), TEXT("WindowPositionY"), WindowPositionY))
		{
			Options.WindowPositionY = WindowPositionY;
		}

		FString MonitorName;
		if (ConfigFile.GetString(TEXT("DruidsSage"), TEXT("MonitorName"), MonitorName))
		{
			Options.MonitorName = MonitorName;
		}

		float MinWindowWidth;
		if (ConfigFile.GetFloat(TEXT("DruidsSage"), TEXT("MinWindowWidth"), MinWindowWidth))
		{
			Options.MinWindowWidth = MinWindowWidth;
		}

		float MinWindowHeight;
		if (ConfigFile.GetFloat(TEXT("DruidsSage"), TEXT("MinWindowHeight"), MinWindowHeight))
		{
			Options.MinWindowHeight = MinWindowHeight;
		}
	}
	else
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Failed to load DruidsSageConfig.ini from %s. Using default values."), *IniFilePath);
	}

	// Load API key from DruidsAuth.ini
	FString AuthIniPath = FPaths::Combine(FPaths::ProjectDir(), TEXT("DruidsAuth.ini"));
	
	// Load the DruidsAuth.ini file
	FConfigFile AuthFile;
	if (FConfigCacheIni::LoadLocalIniFile(AuthFile, *AuthIniPath, false))
	{
		FString DruidsUserToken;
		if (AuthFile.GetString(TEXT("Druids"), TEXT("DruidsUserToken"), DruidsUserToken))
		{
			Options.APIKey = *DruidsUserToken;
		}
		else
		{
			UE_LOG(LogDruidsSage, Warning, TEXT("DruidsUserToken not found in DruidsAuth.ini. Authentication may fail."));
		}
	}
	else
	{
		UE_LOG(LogDruidsSage, Warning, TEXT("Failed to load DruidsAuth.ini from %s. Authentication may fail."), *AuthIniPath);
	}

	return Options;
}
