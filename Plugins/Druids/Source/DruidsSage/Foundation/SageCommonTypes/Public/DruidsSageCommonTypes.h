#pragma once

#include <CoreMinimal.h>

#include "DruidsSageCommonTypes.generated.h"

USTRUCT(BlueprintType, Category = "DruidsSage | Common", Meta = (DisplayName = "DruidsSage Common Error"))
struct SAGECOMMONTYPES_API FDruidsSageCommonError
{
	GENERATED_BODY()

	FDruidsSageCommonError() = default;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	FName Type;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	FName Code;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	FString Message;
};

USTRUCT(BlueprintType, Category = "DruidsSage | Common", Meta = (DisplayName = "DruidsSage Common Options"))
struct SAGECOMMONTYPES_API FDruidsSageCommonOptions
{
	GENERATED_BODY()

	FDruidsSageCommonOptions();

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common", Meta = (DisplayName = "API Key"))
	FName APIKey;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	float WindowWidth = 1200.0f;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	float WindowHeight = 800.0f;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	float WindowPositionX = -1.0f; // -1 indicates not set/use default

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	float WindowPositionY = -1.0f; // -1 indicates not set/use default

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	FString MonitorName; // Name/ID of the monitor the window was last on

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	float MinWindowWidth = 400.0f;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "DruidsSage | Common")
	float MinWindowHeight = 300.0f;

private:
	void SetDefaults();
};
